#include "Motor.h"

// 电机控制状态
static Motor_Control_t motor_state = {0, 0, MOTOR_STOP, MOTOR_STOP};

/**
 * 函数功能：电机初始化
 * 配置PWM定时器和GPIO引脚
 */
void Motor_Init(void)
{
    // PWM定时器已在系统配置中初始化
    // 这里只需要初始化电机状态
    motor_state.left_speed = 0;
    motor_state.right_speed = 0;
    motor_state.left_dir = MOTOR_STOP;
    motor_state.right_dir = MOTOR_STOP;
    
    // 初始化时停止电机
    Motor_Stop();
}

/**
 * 函数功能：设置电机速度
 * 参数：left_speed - 左电机速度 (0-1000)
 *       right_speed - 右电机速度 (0-1000)
 */
void Motor_Set_Speed(uint16_t left_speed, uint16_t right_speed)
{
    // 限制速度范围
    if(left_speed > MOTOR_MAX_SPEED) left_speed = MOTOR_MAX_SPEED;
    if(right_speed > MOTOR_MAX_SPEED) right_speed = MOTOR_MAX_SPEED;
    
    motor_state.left_speed = left_speed;
    motor_state.right_speed = right_speed;
    
    // 设置PWM占空比 (基于MSPM0G3507实际硬件)
    DL_Timer_setCaptureCompareValue(MOTOR_TIMER_INST, left_speed, LEFT_MOTOR_CC_INDEX);
    DL_Timer_setCaptureCompareValue(MOTOR_TIMER_INST, right_speed, RIGHT_MOTOR_CC_INDEX);
}

/**
 * 函数功能：设置电机方向
 * 参数：left_dir - 左电机方向
 *       right_dir - 右电机方向
 */
void Motor_Set_Direction(Motor_Direction_t left_dir, Motor_Direction_t right_dir)
{
    motor_state.left_dir = left_dir;
    motor_state.right_dir = right_dir;
    
    // 左电机方向控制 (基于引脚表: PA13, PA14)
    switch(left_dir) {
        case MOTOR_FORWARD:
            DL_GPIO_setPins(LEFT_MOTOR_DIR1_PORT, LEFT_MOTOR_DIR1_PIN);
            DL_GPIO_clearPins(LEFT_MOTOR_DIR2_PORT, LEFT_MOTOR_DIR2_PIN);
            break;
        case MOTOR_BACKWARD:
            DL_GPIO_clearPins(LEFT_MOTOR_DIR1_PORT, LEFT_MOTOR_DIR1_PIN);
            DL_GPIO_setPins(LEFT_MOTOR_DIR2_PORT, LEFT_MOTOR_DIR2_PIN);
            break;
        case MOTOR_STOP:
            DL_GPIO_clearPins(LEFT_MOTOR_DIR1_PORT, LEFT_MOTOR_DIR1_PIN);
            DL_GPIO_clearPins(LEFT_MOTOR_DIR2_PORT, LEFT_MOTOR_DIR2_PIN);
            break;
    }
    
    // 右电机方向控制 (基于引脚表: PA16, PA17)
    switch(right_dir) {
        case MOTOR_FORWARD:
            DL_GPIO_setPins(RIGHT_MOTOR_DIR1_PORT, RIGHT_MOTOR_DIR1_PIN);
            DL_GPIO_clearPins(RIGHT_MOTOR_DIR2_PORT, RIGHT_MOTOR_DIR2_PIN);
            break;
        case MOTOR_BACKWARD:
            DL_GPIO_clearPins(RIGHT_MOTOR_DIR1_PORT, RIGHT_MOTOR_DIR1_PIN);
            DL_GPIO_setPins(RIGHT_MOTOR_DIR2_PORT, RIGHT_MOTOR_DIR2_PIN);
            break;
        case MOTOR_STOP:
            DL_GPIO_clearPins(RIGHT_MOTOR_DIR1_PORT, RIGHT_MOTOR_DIR1_PIN);
            DL_GPIO_clearPins(RIGHT_MOTOR_DIR2_PORT, RIGHT_MOTOR_DIR2_PIN);
            break;
    }
}

/**
 * 函数功能：综合电机控制
 */
void Motor_Control(uint16_t left_speed, uint16_t right_speed, 
                   Motor_Direction_t left_dir, Motor_Direction_t right_dir)
{
    Motor_Set_Direction(left_dir, right_dir);
    Motor_Set_Speed(left_speed, right_speed);
}

/**
 * 函数功能：前进运动
 */
void Motor_Move_Forward(uint16_t speed)
{
    Motor_Control(speed, speed, MOTOR_FORWARD, MOTOR_FORWARD);
}

/**
 * 函数功能：后退运动
 */
void Motor_Move_Backward(uint16_t speed)
{
    Motor_Control(speed, speed, MOTOR_BACKWARD, MOTOR_BACKWARD);
}

/**
 * 函数功能：左转
 */
void Motor_Turn_Left(uint16_t speed)
{
    Motor_Control(speed/2, speed, MOTOR_FORWARD, MOTOR_FORWARD);
}

/**
 * 函数功能：右转
 */
void Motor_Turn_Right(uint16_t speed)
{
    Motor_Control(speed, speed/2, MOTOR_FORWARD, MOTOR_FORWARD);
}

/**
 * 函数功能：停止
 */
void Motor_Stop(void)
{
    Motor_Control(0, 0, MOTOR_STOP, MOTOR_STOP);
}

/**
 * 函数功能：差速驱动控制
 * 参数：left_speed - 左电机速度 (-1000 到 +1000)
 *       right_speed - 右电机速度 (-1000 到 +1000)
 * 说明：正值表示前进，负值表示后退
 */
void Motor_Differential_Drive(int16_t left_speed, int16_t right_speed)
{
    Motor_Direction_t left_dir, right_dir;
    uint16_t left_abs, right_abs;
    
    // 确定左电机方向和速度
    if(left_speed >= 0) {
        left_dir = MOTOR_FORWARD;
        left_abs = (uint16_t)left_speed;
    } else {
        left_dir = MOTOR_BACKWARD;
        left_abs = (uint16_t)(-left_speed);
    }
    
    // 确定右电机方向和速度
    if(right_speed >= 0) {
        right_dir = MOTOR_FORWARD;
        right_abs = (uint16_t)right_speed;
    } else {
        right_dir = MOTOR_BACKWARD;
        right_abs = (uint16_t)(-right_speed);
    }
    
    // 速度限制
    if(left_abs > MOTOR_MAX_SPEED) left_abs = MOTOR_MAX_SPEED;
    if(right_abs > MOTOR_MAX_SPEED) right_abs = MOTOR_MAX_SPEED;
    
    // 执行控制
    Motor_Control(left_abs, right_abs, left_dir, right_dir);
}