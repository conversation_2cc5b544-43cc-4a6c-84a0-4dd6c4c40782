#ifndef __LINE_FOLLOW_H
#define __LINE_FOLLOW_H

#include "ti_msp_dl_config.h"
#include "No_Mcu_Ganv_Grayscale_Sensor_Config.h"
#include "Motor.h"

/***************************** 循迹参数配置 *****************************/
#define LINE_FOLLOW_BASE_SPEED      600     // 基础行驶速度
#define LINE_FOLLOW_MAX_SPEED       900     // 最大速度
#define LINE_FOLLOW_MIN_SPEED       200     // 最小速度

// PID控制参数
#define PID_KP                      2.0f    // 比例系数
#define PID_KI                      0.1f    // 积分系数
#define PID_KD                      1.5f    // 微分系数

// 循迹状态定义
typedef enum {
    LINE_FOLLOW_STRAIGHT = 0,    // 直行状态
    LINE_FOLLOW_LEFT_TURN,       // 左转状态
    LINE_FOLLOW_RIGHT_TURN,      // 右转状态
    LINE_FOLLOW_SHARP_LEFT,      // 急左转
    LINE_FOLLOW_SHARP_RIGHT,     // 急右转
    LINE_FOLLOW_LOST,            // 丢线状态
    LINE_FOLLOW_STOP             // 停止状态
} LineFollow_State_t;

// PID控制器结构体
typedef struct {
    float kp, ki, kd;           // PID参数
    float error;                // 当前误差
    float last_error;           // 上次误差
    float integral;             // 积分项
    float derivative;           // 微分项
    float output;               // 控制输出
} PID_Controller_t;

// 循迹控制器结构体
typedef struct {
    float sensor_weights[8];     // 传感器权重
    float position;              // 计算出的位置
    float last_position;         // 上次位置
    LineFollow_State_t state;    // 当前状态
    PID_Controller_t pid;        // PID控制器
    uint16_t left_motor_speed;   // 左电机速度
    uint16_t right_motor_speed;  // 右电机速度
    uint8_t line_detected;       // 检测到线标志
    uint8_t sensor_count;        // 检测到线的传感器数量
} LineFollow_Controller_t;

#ifdef __cplusplus
extern "C" {
#endif

/***************************** 函数声明 *****************************/
// 初始化函数
void LineFollow_Init(LineFollow_Controller_t* controller);
void LineFollow_PID_Init(PID_Controller_t* pid, float kp, float ki, float kd);

// 核心算法函数
float LineFollow_Calculate_Position(LineFollow_Controller_t* controller, unsigned char digital_data);
float LineFollow_PID_Calculate(PID_Controller_t* pid, float setpoint, float measured_value);
void LineFollow_State_Machine(LineFollow_Controller_t* controller);

// 控制函数
void LineFollow_Control(LineFollow_Controller_t* controller, No_MCU_Sensor* sensor);
void LineFollow_Motor_Control(LineFollow_Controller_t* controller);

// 状态检测函数
LineFollow_State_t LineFollow_Detect_State(unsigned char digital_data);
uint8_t LineFollow_Count_Active_Sensors(unsigned char digital_data);

// 调试函数
void LineFollow_Debug_Print(LineFollow_Controller_t* controller);

#ifdef __cplusplus
}
#endif

#endif /* __LINE_FOLLOW_H */