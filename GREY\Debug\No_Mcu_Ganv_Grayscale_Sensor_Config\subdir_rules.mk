################################################################################
# Automatically-generated file. Do not edit!
################################################################################

SHELL = cmd.exe

# Each subdirectory must supply rules for building sources it contributes
No_Mcu_Ganv_Grayscale_Sensor_Config/%.o: ../No_Mcu_Ganv_Grayscale_Sensor_Config/%.c $(GEN_OPTS) | $(GEN_FILES) $(GEN_MISC_FILES)
	@echo 'Building file: "$<"'
	@echo 'Invoking: Arm Compiler'
	"C:/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/bin/tiarmclang.exe" -c @"device.opt"  -march=thumbv6m -mcpu=cortex-m0plus -mfloat-abi=soft -mlittle-endian -mthumb -O0 -I"C:/Users/<USER>/Desktop/GREY_/GREY/Time" -I"C:/Users/<USER>/Desktop/GREY_/GREY/ADC" -I"C:/Users/<USER>/Desktop/GREY_/GREY" -I"C:/Users/<USER>/Desktop/GREY_/GREY/Debug" -I"C:/ti/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include" -I"C:/ti/mspm0_sdk_2_05_00_05/source" -I"C:/Users/<USER>/Desktop/GREY_/GREY/No_Mcu_Ganv_Grayscale_Sensor_Config" -I"C:/Users/<USER>/Desktop/GREY_/GREY/UART" -gdwarf-3 -MMD -MP -MF"No_Mcu_Ganv_Grayscale_Sensor_Config/$(basename $(<F)).d_raw" -MT"$(@)"  $(GEN_OPTS__FLAG) -o"$@" "$<"
	@echo 'Finished building: "$<"'
	@echo ' '


