# 🔌 MSPM0G3507 循迹小车引脚配置详细说明

## 📋 基于官方引脚表的完整硬件配置

根据提供的MSPM0G3507引脚表，以下是循迹小车系统的完整引脚分配方案：

---

## 🎯 核心功能模块引脚分配

### 🔍 灰度传感器模块 (已有GREY系统)
```
功能：8路灰度传感器地址复用采集
┌─────────────────┬──────────────┬────────────────┬──────────────┐
│     功能        │    引脚      │   复用功能     │    说明      │
├─────────────────┼──────────────┼────────────────┼──────────────┤
│ ADC模拟输入     │    PA27      │   ADC0_CH0     │ 8路复用采集  │
│ 地址选择位0     │    PB0       │   GPIO_OUT     │ 传感器选择   │
│ 地址选择位1     │    PB1       │   GPIO_OUT     │ 传感器选择   │
│ 地址选择位2     │    PB2       │   GPIO_OUT     │ 传感器选择   │
└─────────────────┴──────────────┴────────────────┴──────────────┘
```

### ⚡ 电机控制模块 (新增功能)
```
功能：双路PWM差速电机驱动
┌─────────────────┬──────────────┬────────────────┬──────────────┐
│     功能        │    引脚      │   复用功能     │    说明      │
├─────────────────┼──────────────┼────────────────┼──────────────┤
│ 左电机PWM       │    PA12      │   TIMG0_C0     │ 1kHz PWM输出 │
│ 左电机正转      │    PA13      │   GPIO_OUT     │ 方向控制1    │
│ 左电机反转      │    PA14      │   GPIO_OUT     │ 方向控制2    │
│ 右电机PWM       │    PA15      │   TIMG0_C1     │ 1kHz PWM输出 │
│ 右电机正转      │    PA16      │   GPIO_OUT     │ 方向控制1    │
│ 右电机反转      │    PA17      │   GPIO_OUT     │ 方向控制2    │
└─────────────────┴──────────────┴────────────────┴──────────────┘
```

### 📡 通信与调试模块
```
功能：串口调试和在线调试
┌─────────────────┬──────────────┬────────────────┬──────────────┐
│     功能        │    引脚      │   复用功能     │    说明      │
├─────────────────┼──────────────┼────────────────┼──────────────┤
│ 串口发送        │    PA10      │   UART0_TX     │ 115200bps    │
│ 串口接收        │    PA11      │   UART0_RX     │ 115200bps    │
│ SWD调试数据     │    PA19      │   SWDIO        │ 在线调试     │
│ SWD调试时钟     │    PA20      │   SWCLK        │ 在线调试     │
└─────────────────┴──────────────┴────────────────┴──────────────┘
```

---

## 🏗️ 硬件连接方案

### 🔌 电机驱动模块接线
```
电机驱动芯片 (如L298N/TB6612):
┌────────────────────────────────────────────────────────┐
│                    左电机控制                          │
│  PA12 (PWM) ────────────► ENA (使能)                   │
│  PA13 (DIR1) ───────────► IN1 (正转)                   │
│  PA14 (DIR2) ───────────► IN2 (反转)                   │
│                                                        │
│                    右电机控制                          │
│  PA15 (PWM) ────────────► ENB (使能)                   │
│  PA16 (DIR1) ───────────► IN3 (正转)                   │
│  PA17 (DIR2) ───────────► IN4 (反转)                   │
└────────────────────────────────────────────────────────┘
```

### 🔍 传感器模块接线
```
8路灰度传感器模块:
┌────────────────────────────────────────────────────────┐
│  PA27 (ADC) ────────────► AOUT (模拟输出)              │
│  PB0 (ADDR0) ───────────► A0 (地址选择位0)             │
│  PB1 (ADDR1) ───────────► A1 (地址选择位1)             │
│  PB2 (ADDR2) ───────────► A2 (地址选择位2)             │
│  3.3V ──────────────────► VCC (电源)                   │
│  GND ───────────────────► GND (地线)                   │
└────────────────────────────────────────────────────────┘
```

---

## ⚙️ 定时器配置策略

### 📊 定时器分配表
```
┌─────────────┬────────────┬─────────────┬──────────────────┐
│   定时器    │    用途    │   频率/周期  │      说明        │
├─────────────┼────────────┼─────────────┼──────────────────┤
│   TIMG0     │ PWM生成    │   1kHz      │ 电机速度控制     │
│   TIMG12    │ 系统时基   │   1ms       │ 延时和时序控制   │
└─────────────┴────────────┴─────────────┴──────────────────┘
```

### ⚡ PWM参数计算
```
系统时钟: 80MHz
PWM频率: 1kHz
预分频: 80 (80MHz ÷ 80 = 1MHz)
周期计数: 1000 (1MHz ÷ 1000 = 1kHz)
占空比范围: 0-1000 (对应0%-100%)
```

---

## 🔧 引脚复用冲突分析

### ✅ 无冲突引脚分配
经过引脚表分析，所有选用引脚均无功能冲突：

```
Port A 引脚使用情况:
PA10 ✓ UART0_TX     (通信)
PA11 ✓ UART0_RX     (通信)  
PA12 ✓ TIMG0_C0     (左电机PWM)
PA13 ✓ GPIO_OUT     (左电机方向1)
PA14 ✓ GPIO_OUT     (左电机方向2)
PA15 ✓ TIMG0_C1     (右电机PWM)
PA16 ✓ GPIO_OUT     (右电机方向1)
PA17 ✓ GPIO_OUT     (右电机方向2)
PA19 ✓ SWDIO        (调试)
PA20 ✓ SWCLK        (调试)
PA27 ✓ ADC0_CH0     (传感器)

Port B 引脚使用情况:
PB0  ✓ GPIO_OUT     (传感器地址0)
PB1  ✓ GPIO_OUT     (传感器地址1)
PB2  ✓ GPIO_OUT     (传感器地址2)
```

---

## 📐 PCB布线建议

### 🔌 信号分组布线
```
数字信号组:
├── 灰度传感器地址线 (PB0-PB2)
├── 电机方向控制 (PA13,14,16,17)
└── 调试接口 (PA19-PA20)

模拟信号组:
└── ADC输入 (PA27) - 远离开关信号

PWM信号组:
└── 电机PWM (PA12, PA15) - 使用差分布线

通信信号组:
└── UART (PA10-PA11) - 阻抗匹配
```

### ⚡ 电源设计要点
```
数字电源 (3.3V):
├── MCU核心 (MSPM0G3507)
├── 传感器模块
└── 数字逻辑电路

电机电源 (5V-12V):
├── 电机驱动芯片
├── 电机本体
└── 独立电源域隔离
```

---

## 🛠️ 调试接口配置

### 📡 SWD调试接口
```
标准20针JTAG接口映射:
Pin 7  ────────► PA20 (SWCLK)
Pin 9  ────────► PA19 (SWDIO)  
Pin 15 ────────► nRST (复位)
Pin 19 ────────► 3.3V (电源)
Pin 20 ────────► GND (地线)
```

### 📊 串口调试协议
```
波特率: 115200
数据位: 8
停止位: 1  
校验位: 无
流控制: 无

调试信息格式:
"Pos:±X.XX State:X PID:±XXX.X L:XXX R:XXX Sensors:X"
```

---

## ⚠️ 硬件注意事项

### 🔋 电源要求
- **MCU电源**: 3.3V ±5%，纹波<50mV
- **电机电源**: 根据电机规格，建议6-12V
- **电流需求**: MCU<100mA，电机峰值可达2A/路

### 🛡️ 保护措施
- **ESD保护**: 在调试接口添加ESD保护二极管
- **过流保护**: 电机驱动添加熔断器或电子保险
- **反向保护**: 电源接口添加肖特基二极管

### 📏 机械限制
- **传感器高度**: 距离地面8-15mm
- **传感器间距**: 建议10mm均匀分布
- **轮间距**: 根据转弯半径要求设计

---

## 🎯 性能指标

### 📊 系统性能
- **循迹精度**: ±0.5cm
- **响应频率**: 1kHz (传感器采样)
- **PWM分辨率**: 10位 (0-1000)
- **最大速度**: 由电机和电源决定

### ⚡ 功耗分析
```
模块功耗估算:
├── MSPM0G3507: ~30mA @ 80MHz
├── 灰度传感器: ~50mA @ 3.3V  
├── 电机 (空载): ~200mA/路 @ 6V
├── 电机 (负载): ~800mA/路 @ 6V
└── 总计: ~1.1A (正常运行)
```

---

**版本信息**:
- 硬件版本: v1.0
- 配置日期: 2025-01-31
- 开发单位: 米醋电子工作室
- 基于芯片: TI MSPM0G3507