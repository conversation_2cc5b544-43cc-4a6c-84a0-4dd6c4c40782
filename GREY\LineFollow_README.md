# 🚗 循迹小车系统说明文档

## 📋 项目概述

基于 GREY 8路灰度传感器的循迹小车系统，采用权重算法和PID控制实现精确循迹。

**技术特性**:
- 硬件平台: TI MSPM0G3507 @ 80MHz
- 传感器: 8路灰度传感器 (地址复用)
- 控制算法: 权重位置计算 + PID差速控制
- 通信: UART 115200bps 调试输出

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   8路灰度传感器   │───▶│   位置计算算法   │───▶│   PID控制器     │
│  (权重-3.5~+3.5) │    │  (加权平均)     │    │  (差速控制)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                      │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   串口调试输出   │◀───│   状态机控制     │◀───│   PWM电机驱动   │
│  (实时监控)     │    │  (多模式)       │    │  (左右轮)       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🔌 引脚配置

### 传感器接口
- **ADC输入**: PA27 (8路复用)
- **地址选择**: PB0-PB2 (3位地址线)

### 电机控制接口
- **左电机PWM**: 需配置定时器PWM输出
- **左电机方向**: PA0(前进), PA1(后退)
- **右电机PWM**: 需配置定时器PWM输出  
- **右电机方向**: PA2(前进), PA3(后退)

### 通信接口
- **串口调试**: PA10(TX), PA11(RX) @ 115200bps

## ⚙️ 核心算法

### 1. 权重位置计算
```c
// 传感器权重分配
float sensor_weights[8] = {-3.5, -2.5, -1.5, -0.5, 0.5, 1.5, 2.5, 3.5};

// 位置计算公式
position = Σ(sensor_value[i] * weight[i]) / Σ(sensor_value[i])
```

### 2. PID控制算法
```c
// PID计算
error = setpoint - measured_position;
integral += error;
derivative = error - last_error;
output = Kp*error + Ki*integral + Kd*derivative;

// 电机速度计算
left_speed = base_speed - output;
right_speed = base_speed + output;
```

### 3. 状态机控制
- **直行状态**: 中间传感器激活，PID精确控制
- **左转状态**: 左侧传感器激活，减小左轮速度
- **右转状态**: 右侧传感器激活，减小右轮速度
- **急转状态**: 多传感器激活，大幅度转向
- **丢线状态**: 无传感器激活，基于最后位置搜索

## 🚀 使用说明

### 1. 系统启动
```
=== 循迹小车系统启动 ===
芯片: MSPM0G3507 @ 80MHz
传感器: 8路灰度传感器
控制: PWM差速驱动
米醋电子工作室制作
```

### 2. 校准模式 (前10秒)
- 系统自动采集传感器数据
- 串口输出校准信息用于调整阈值
- 可根据实际环境修改 `white[]` 和 `black[]` 数组

### 3. 循迹模式
- 自动进入循迹状态
- 实时调试信息输出:
```
Pos:0.50 State:1 PID:25.3 L:575 R:625 Sensors:3
```

## 🔧 参数调优

### PID参数调整
```c
#define PID_KP  2.0f    // 比例系数 (响应速度)
#define PID_KI  0.1f    // 积分系数 (稳态精度)  
#define PID_KD  1.5f    // 微分系数 (稳定性)
```

### 速度参数调整
```c
#define LINE_FOLLOW_BASE_SPEED  600   // 基础速度
#define LINE_FOLLOW_MAX_SPEED   900   // 最大速度
#define LINE_FOLLOW_MIN_SPEED   200   // 最小速度
```

### 传感器权重调整
根据实际传感器间距调整权重值，保证线性分布。

## 📊 性能指标

- **循迹精度**: ±0.5cm (基于权重算法)
- **响应频率**: 1kHz (经典版传感器)
- **最大速度**: 可调 (建议600-900 PWM值)
- **转弯半径**: 动态调整 (基于PID输出)

## 🛠️ 硬件要求

### 必需硬件
- TI MSPM0G3507 开发板
- 8路灰度传感器模块
- 双电机小车底盘
- 电机驱动模块 (支持PWM+方向控制)
- 电源模块 (3.3V/5V)

### 可选硬件
- OLED显示屏 (状态显示)
- 按键模块 (模式切换)
- 蜂鸣器 (状态提示)

## 🔍 调试方法

### 1. 串口调试
- 波特率: 115200
- 数据位: 8
- 停止位: 1
- 校验位: 无

### 2. 调试信息
- **Pos**: 计算位置 (-3.5 到 +3.5)
- **State**: 当前状态 (0-6)
- **PID**: PID控制输出
- **L/R**: 左右电机速度
- **Sensors**: 激活传感器数量

### 3. 常见问题
- **丢线**: 检查传感器高度和阈值设置
- **震荡**: 减小PID_KP或增大PID_KD
- **转弯不够**: 增大权重值或调整电机速度差
- **过冲**: 减小基础速度或调整PID参数

## 📈 性能优化建议

1. **传感器优化**:
   - 调整传感器高度 (推荐8-15mm)
   - 校准黑白阈值
   - 检查传感器一致性

2. **算法优化**:
   - 根据赛道特点调整PID参数
   - 优化权重分配
   - 添加前瞻控制

3. **机械优化**:
   - 调整轮间距
   - 优化重心位置
   - 减少机械间隙

## 📞 技术支持

- 开发单位: 米醋电子工作室 (Michu Electronics Studio)
- 技术支持: <EMAIL>
- GitHub: https://github.com/McuXifeng
- 版本: v1.0
- 更新日期: 2025-01-31