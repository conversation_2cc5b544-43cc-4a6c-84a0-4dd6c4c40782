******************************************************************************
            TI ARM Clang Linker PC v4.0.2                      
******************************************************************************
>> Linked Thu Jul 31 15:05:28 2025

OUTPUT FILE NAME:   <GREY.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 000015f5


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00001c08  0001e3f8  R  X
  SRAM                  20200000   00008000  00000340  00007cc0  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00001c08   00001c08    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00001a28   00001a28    r-x .text
  00001ae8    00001ae8    000000d0   000000d0    r-- .rodata
  00001bb8    00001bb8    00000050   00000050    r-- .cinit
20200000    20200000    00000140   00000000    rw-
  20200000    20200000    00000130   00000000    rw- .data
  20200130    20200130    00000010   00000000    rw- .bss
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00001a28     
                  000000c0    00000284     libc.a : _printfi.c.obj (.text:__TI_printfi_minimal)
                  00000344    00000194     GREY.o (.text.main)
                  000004d8    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  0000066a    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  0000066c    00000188     No_Mcu_Ganv_Grayscale_Sensor_Config.o (.text.No_MCU_Ganv_Sensor_Init)
                  000007f4    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00000900    000000e8     driverlib.a : dl_timer.o (.text.DL_Timer_initTimerMode)
                  000009e8    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00000acc    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00000ba8    000000d0     No_Mcu_Ganv_Grayscale_Sensor_Config.o (.text.Get_Analog_value)
                  00000c78    000000aa     No_Mcu_Ganv_Grayscale_Sensor_Config.o (.text.normalizeAnalogValues)
                  00000d22    00000002     --HOLE-- [fill = 0]
                  00000d24    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00000da0    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00000e14    00000072     No_Mcu_Ganv_Grayscale_Sensor_Config.o (.text.No_MCU_Ganv_Sensor_Init_Frist)
                  00000e86    0000006c     No_Mcu_Ganv_Grayscale_Sensor_Config.o (.text.convertAnalogToDigital)
                  00000ef2    00000002     --HOLE-- [fill = 0]
                  00000ef4    00000068     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00000f5c    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00000fc4    00000062                            : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00001026    00000002     --HOLE-- [fill = 0]
                  00001028    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00001074    0000004c     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC1_init)
                  000010c0    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  0000110a    00000002     --HOLE-- [fill = 0]
                  0000110c    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00001156    0000004a     ADC.o (.text.adc_getValue)
                  000011a0    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  000011e8    00000044                 : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  0000122c    00000044     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00001270    00000042     No_Mcu_Ganv_Grayscale_Sensor_Config.o (.text.No_Mcu_Ganv_Sensor_Task_Without_tick)
                  000012b2    00000002     --HOLE-- [fill = 0]
                  000012b4    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  000012f4    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  00001334    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00001374    0000003c     No_Mcu_Ganv_Grayscale_Sensor_Config.o (.text.Get_Anolog_Value)
                  000013b0    0000003c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  000013ec    0000003c     Uart.o (.text.uart0_send_string)
                  00001428    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00001464    0000003a     libclang_rt.builtins.a : muldsi3.S.obj (.text.__muldsi3)
                  0000149e    00000038     No_Mcu_Ganv_Grayscale_Sensor_Config.o (.text.Get_Normalize_For_User)
                  000014d6    00000002     --HOLE-- [fill = 0]
                  000014d8    00000038     libc.a : sprintf.c.obj (.text.sprintf)
                  00001510    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_1_init)
                  00001544    00000030     ADC.o (.text.DL_ADC12_getMemResult)
                  00001574    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  000015a0    0000002c     Uart.o (.text.uart0_send_char)
                  000015cc    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  000015f4    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  0000161c    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  00001640    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00001662    00000002     --HOLE-- [fill = 0]
                  00001664    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00001684    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  000016a4    0000001e     ti_msp_dl_config.o (.text.DL_ADC12_setPowerDownMode)
                  000016c2    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  000016e0    0000001c     ADC.o (.text.DL_ADC12_startConversion)
                  000016fc    0000001c     ADC.o (.text.DL_ADC12_stopConversion)
                  00001718    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00001734    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00001750    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  0000176c    0000001c     ti_msp_dl_config.o (.text.DL_Timer_enableInterrupt)
                  00001788    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setClockConfig)
                  000017a4    0000001c     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  000017c0    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  000017d8    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  000017f0    00000018     ti_msp_dl_config.o (.text.DL_ADC12_setSampleTime0)
                  00001808    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00001820    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00001838    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00001850    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00001868    00000018     No_Mcu_Ganv_Grayscale_Sensor_Config.o (.text.DL_GPIO_setPins)
                  00001880    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00001898    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  000018b0    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  000018c8    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  000018e0    00000018     Uart.o (.text.DL_UART_isBusy)
                  000018f8    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00001910    00000018     libc.a : sprintf.c.obj (.text._outs)
                  00001928    00000016     ADC.o (.text.DL_ADC12_disableConversions)
                  0000193e    00000016     ADC.o (.text.DL_ADC12_enableConversions)
                  00001954    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  0000196a    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00001980    00000016     Uart.o (.text.DL_UART_transmitData)
                  00001996    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  000019ac    00000014     No_Mcu_Ganv_Grayscale_Sensor_Config.o (.text.DL_GPIO_clearPins)
                  000019c0    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  000019d4    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  000019e8    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  000019fc    00000014     Time.o (.text.delay_ms)
                  00001a10    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00001a22    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00001a34    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00001a46    00000010     ADC.o (.text.DL_ADC12_getStatus)
                  00001a56    00000002     --HOLE-- [fill = 0]
                  00001a58    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00001a68    0000000e     No_Mcu_Ganv_Grayscale_Sensor_Config.o (.text.Get_Digtal_For_User)
                  00001a76    00000002     --HOLE-- [fill = 0]
                  00001a78    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  00001a86    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  00001a94    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  00001aa2    00000002     --HOLE-- [fill = 0]
                  00001aa4    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  00001ab0    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00001aba    0000000a     libc.a : sprintf.c.obj (.text._outc)
                  00001ac4    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00001acc    00000006     libc.a : exit.c.obj (.text:abort)
                  00001ad2    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00001ad6    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00001ada    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00001ade    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  00001ae2    00000006     --HOLE-- [fill = 0]

.cinit     0    00001bb8    00000050     
                  00001bb8    0000002c     (.cinit..data.load) [load image, compression = lzss]
                  00001be4    0000000c     (__TI_handler_table)
                  00001bf0    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00001bf8    00000010     (__TI_cinit_table)

.rodata    0    00001ae8    000000d0     
                  00001ae8    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00001b10    00000024     GREY.o (.rodata.str1.12272544578313487268.1)
                  00001b34    00000021     GREY.o (.rodata.str1.17942031646099919906.1)
                  00001b55    00000021     GREY.o (.rodata.str1.4249431404602163476.1)
                  00001b76    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  00001b78    00000014     ti_msp_dl_config.o (.rodata.gTIMER_1TimerConfig)
                  00001b8c    00000011     libc.a : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  00001b9d    00000001     --HOLE-- [fill = 0]
                  00001b9e    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  00001ba8    00000008     ti_msp_dl_config.o (.rodata.gADC1ClockConfig)
                  00001bb0    00000003     ti_msp_dl_config.o (.rodata.gTIMER_1ClockConfig)
                  00001bb3    00000005     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.data      0    20200000    00000130     UNINITIALIZED
                  20200000    00000100     GREY.o (.data.rx_buff)
                  20200100    00000010     GREY.o (.data.Anolog)
                  20200110    00000010     GREY.o (.data.black)
                  20200120    00000010     GREY.o (.data.white)

.bss       0    20200130    00000010     UNINITIALIZED
                  20200130    00000010     (.common:Normal)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                                  code   ro data   rw data
       ------                                  ----   -------   -------
    .\
       ti_msp_dl_config.o                      1286   83        0      
       GREY.o                                  404    102       320    
       startup_mspm0g350x_ticlang.o            8      192       0      
    +--+---------------------------------------+------+---------+---------+
       Total:                                  1698   377       320    
                                                                       
    .\ADC\
       ADC.o                                   238    0         0      
    +--+---------------------------------------+------+---------+---------+
       Total:                                  238    0         0      
                                                                       
    .\No_Mcu_Ganv_Grayscale_Sensor_Config\
       No_Mcu_Ganv_Grayscale_Sensor_Config.o   1232   0         0      
    +--+---------------------------------------+------+---------+---------+
       Total:                                  1232   0         0      
                                                                       
    .\Time\
       Time.o                                  20     0         0      
    +--+---------------------------------------+------+---------+---------+
       Total:                                  20     0         0      
                                                                       
    .\UART\
       Uart.o                                  150    0         0      
    +--+---------------------------------------+------+---------+---------+
       Total:                                  150    0         0      
                                                                       
    C:/ti/mspm0_sdk_2_05_00_05/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o            288    0         0      
       dl_timer.o                              260    0         0      
       dl_uart.o                               90     0         0      
       dl_adc12.o                              64     0         0      
       dl_common.o                             10     0         0      
    +--+---------------------------------------+------+---------+---------+
       Total:                                  712    0         0      
                                                                       
    C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                          658    17        0      
       copy_decompress_lzss.c.obj              124    0         0      
       sprintf.c.obj                           90     0         0      
       autoinit.c.obj                          60     0         0      
       boot_cortex_m.c.obj                     40     0         0      
       memccpy.c.obj                           34     0         0      
       copy_zero_init.c.obj                    22     0         0      
       copy_decompress_none.c.obj              18     0         0      
       memcpy16.S.obj                          18     0         0      
       memset16.S.obj                          14     0         0      
       exit.c.obj                              6      0         0      
       pre_init.c.obj                          4      0         0      
    +--+---------------------------------------+------+---------+---------+
       Total:                                  1088   17        0      
                                                                       
    C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                          4      0         0      
    +--+---------------------------------------+------+---------+---------+
       Total:                                  4      0         0      
                                                                       
    C:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                            402    0         0      
       divdf3.S.obj                            268    0         0      
       muldf3.S.obj                            228    0         0      
       comparedf2.c.obj                        220    0         0      
       aeabi_dcmp.S.obj                        98     0         0      
       fixdfsi.S.obj                           74     0         0      
       aeabi_uidivmod.S.obj                    64     0         0      
       muldsi3.S.obj                           58     0         0      
       floatsidf.S.obj                         44     0         0      
       floatunsidf.S.obj                       36     0         0      
       aeabi_memset.S.obj                      26     0         0      
       aeabi_memcpy.S.obj                      8      0         0      
       aeabi_div0.c.obj                        2      0         0      
    +--+---------------------------------------+------+---------+---------+
       Total:                                  1528   0         0      
                                                                       
       Stack:                                  0      0         512    
       Linker Generated:                       0      80        0      
    +--+---------------------------------------+------+---------+---------+
       Grand Total:                            6670   474       832    


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00001bf8 records: 2, size/record: 8, table size: 16
	.data: load addr=00001bb8, load size=0000002c bytes, run addr=20200000, run size=00000130 bytes, compression=lzss
	.bss: load addr=00001bf0, load size=00000008 bytes, run addr=20200130, run size=00000010 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00001be4 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
00001ad3  ADC0_IRQHandler                      
00001ad3  ADC1_IRQHandler                      
00001ad3  AES_IRQHandler                       
20200100  Anolog                               
00001ad6  C$$EXIT                              
00001ad3  CANFD0_IRQHandler                    
00001ad3  DAC0_IRQHandler                      
000012b5  DL_ADC12_setClockConfig              
00001ab1  DL_Common_delayCycles                
00000acd  DL_SYSCTL_configSYSPLL               
000011e9  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00000901  DL_Timer_initTimerMode               
00001789  DL_Timer_setClockConfig              
000011a1  DL_UART_init                         
00001a11  DL_UART_setClockConfig               
00001ad3  DMA_IRQHandler                       
00001ad3  Default_Handler                      
00001ad3  GROUP0_IRQHandler                    
00001ad3  GROUP1_IRQHandler                    
00000ba9  Get_Analog_value                     
00001375  Get_Anolog_Value                     
00001a69  Get_Digtal_For_User                  
0000149f  Get_Normalize_For_User               
00001ad7  HOSTexit                             
00001ad3  HardFault_Handler                    
00001ad3  I2C0_IRQHandler                      
00001ad3  I2C1_IRQHandler                      
00001ad3  NMI_Handler                          
0000066d  No_MCU_Ganv_Sensor_Init              
00000e15  No_MCU_Ganv_Sensor_Init_Frist        
00001271  No_Mcu_Ganv_Sensor_Task_Without_tick 
20200130  Normal                               
00001ad3  PendSV_Handler                       
00001ad3  RTC_IRQHandler                       
00001adb  Reset_Handler                        
00001ad3  SPI0_IRQHandler                      
00001ad3  SPI1_IRQHandler                      
00001ad3  SVC_Handler                          
00001075  SYSCFG_DL_ADC1_init                  
0000122d  SYSCFG_DL_GPIO_init                  
000013b1  SYSCFG_DL_SYSCTL_init                
00001511  SYSCFG_DL_TIMER_1_init               
000012f5  SYSCFG_DL_UART_0_init                
000017a5  SYSCFG_DL_init                       
00000ef5  SYSCFG_DL_initPower                  
00001ad3  SysTick_Handler                      
00001ad3  TIMA0_IRQHandler                     
00001ad3  TIMA1_IRQHandler                     
00001ad3  TIMG0_IRQHandler                     
00001ad3  TIMG12_IRQHandler                    
00001ad3  TIMG6_IRQHandler                     
00001ad3  TIMG7_IRQHandler                     
00001ad3  TIMG8_IRQHandler                     
00001a23  TI_memcpy_small                      
00001a95  TI_memset_small                      
00001ad3  UART0_IRQHandler                     
00001ad3  UART1_IRQHandler                     
00001ad3  UART2_IRQHandler                     
00001ad3  UART3_IRQHandler                     
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00001bf8  __TI_CINIT_Base                      
00001c08  __TI_CINIT_Limit                     
00001c08  __TI_CINIT_Warm                      
00001be4  __TI_Handler_Table_Base              
00001bf0  __TI_Handler_Table_Limit             
00001429  __TI_auto_init_nobinit_nopinit       
00000d25  __TI_decompress_lzss                 
00001a35  __TI_decompress_none                 
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi_minimal                 
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00001997  __TI_zero_init_nomemset              
000004e3  __adddf3                             
0000110d  __aeabi_d2iz                         
000004e3  __aeabi_dadd                         
00000fc5  __aeabi_dcmpeq                       
00001001  __aeabi_dcmpge                       
00001015  __aeabi_dcmpgt                       
00000fed  __aeabi_dcmple                       
00000fd9  __aeabi_dcmplt                       
000007f5  __aeabi_ddiv                         
000009e9  __aeabi_dmul                         
000004d9  __aeabi_dsub                         
00001575  __aeabi_i2d                          
0000066b  __aeabi_idiv0                        
00001aa5  __aeabi_memclr                       
00001aa5  __aeabi_memclr4                      
00001aa5  __aeabi_memclr8                      
00001ac5  __aeabi_memcpy                       
00001ac5  __aeabi_memcpy4                      
00001ac5  __aeabi_memcpy8                      
00001a79  __aeabi_memset                       
00001a79  __aeabi_memset4                      
00001a79  __aeabi_memset8                      
0000161d  __aeabi_ui2d                         
00001335  __aeabi_uidiv                        
00001335  __aeabi_uidivmod                     
ffffffff  __binit__                            
00000f5d  __cmpdf2                             
000007f5  __divdf3                             
00000f5d  __eqdf2                              
0000110d  __fixdfsi                            
00001575  __floatsidf                          
0000161d  __floatunsidf                        
00000da1  __gedf2                              
00000da1  __gtdf2                              
00000f5d  __ledf2                              
00000f5d  __ltdf2                              
UNDEFED   __mpu_init                           
000009e9  __muldf3                             
00001465  __muldsi3                            
00000f5d  __nedf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
000004d9  __subdf3                             
000015f5  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
00001adf  _system_pre_init                     
00001acd  abort                                
00001157  adc_getValue                         
ffffffff  binit                                
20200110  black                                
00000e87  convertAnalogToDigital               
000019fd  delay_ms                             
00000000  interruptVectors                     
00000345  main                                 
00001641  memccpy                              
00000c79  normalizeAnalogValues                
20200000  rx_buff                              
000014d9  sprintf                              
000015a1  uart0_send_char                      
000013ed  uart0_send_string                    
20200120  white                                


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi_minimal                 
00000200  __STACK_SIZE                         
00000345  main                                 
000004d9  __aeabi_dsub                         
000004d9  __subdf3                             
000004e3  __adddf3                             
000004e3  __aeabi_dadd                         
0000066b  __aeabi_idiv0                        
0000066d  No_MCU_Ganv_Sensor_Init              
000007f5  __aeabi_ddiv                         
000007f5  __divdf3                             
00000901  DL_Timer_initTimerMode               
000009e9  __aeabi_dmul                         
000009e9  __muldf3                             
00000acd  DL_SYSCTL_configSYSPLL               
00000ba9  Get_Analog_value                     
00000c79  normalizeAnalogValues                
00000d25  __TI_decompress_lzss                 
00000da1  __gedf2                              
00000da1  __gtdf2                              
00000e15  No_MCU_Ganv_Sensor_Init_Frist        
00000e87  convertAnalogToDigital               
00000ef5  SYSCFG_DL_initPower                  
00000f5d  __cmpdf2                             
00000f5d  __eqdf2                              
00000f5d  __ledf2                              
00000f5d  __ltdf2                              
00000f5d  __nedf2                              
00000fc5  __aeabi_dcmpeq                       
00000fd9  __aeabi_dcmplt                       
00000fed  __aeabi_dcmple                       
00001001  __aeabi_dcmpge                       
00001015  __aeabi_dcmpgt                       
00001075  SYSCFG_DL_ADC1_init                  
0000110d  __aeabi_d2iz                         
0000110d  __fixdfsi                            
00001157  adc_getValue                         
000011a1  DL_UART_init                         
000011e9  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
0000122d  SYSCFG_DL_GPIO_init                  
00001271  No_Mcu_Ganv_Sensor_Task_Without_tick 
000012b5  DL_ADC12_setClockConfig              
000012f5  SYSCFG_DL_UART_0_init                
00001335  __aeabi_uidiv                        
00001335  __aeabi_uidivmod                     
00001375  Get_Anolog_Value                     
000013b1  SYSCFG_DL_SYSCTL_init                
000013ed  uart0_send_string                    
00001429  __TI_auto_init_nobinit_nopinit       
00001465  __muldsi3                            
0000149f  Get_Normalize_For_User               
000014d9  sprintf                              
00001511  SYSCFG_DL_TIMER_1_init               
00001575  __aeabi_i2d                          
00001575  __floatsidf                          
000015a1  uart0_send_char                      
000015f5  _c_int00_noargs                      
0000161d  __aeabi_ui2d                         
0000161d  __floatunsidf                        
00001641  memccpy                              
00001789  DL_Timer_setClockConfig              
000017a5  SYSCFG_DL_init                       
00001997  __TI_zero_init_nomemset              
000019fd  delay_ms                             
00001a11  DL_UART_setClockConfig               
00001a23  TI_memcpy_small                      
00001a35  __TI_decompress_none                 
00001a69  Get_Digtal_For_User                  
00001a79  __aeabi_memset                       
00001a79  __aeabi_memset4                      
00001a79  __aeabi_memset8                      
00001a95  TI_memset_small                      
00001aa5  __aeabi_memclr                       
00001aa5  __aeabi_memclr4                      
00001aa5  __aeabi_memclr8                      
00001ab1  DL_Common_delayCycles                
00001ac5  __aeabi_memcpy                       
00001ac5  __aeabi_memcpy4                      
00001ac5  __aeabi_memcpy8                      
00001acd  abort                                
00001ad3  ADC0_IRQHandler                      
00001ad3  ADC1_IRQHandler                      
00001ad3  AES_IRQHandler                       
00001ad3  CANFD0_IRQHandler                    
00001ad3  DAC0_IRQHandler                      
00001ad3  DMA_IRQHandler                       
00001ad3  Default_Handler                      
00001ad3  GROUP0_IRQHandler                    
00001ad3  GROUP1_IRQHandler                    
00001ad3  HardFault_Handler                    
00001ad3  I2C0_IRQHandler                      
00001ad3  I2C1_IRQHandler                      
00001ad3  NMI_Handler                          
00001ad3  PendSV_Handler                       
00001ad3  RTC_IRQHandler                       
00001ad3  SPI0_IRQHandler                      
00001ad3  SPI1_IRQHandler                      
00001ad3  SVC_Handler                          
00001ad3  SysTick_Handler                      
00001ad3  TIMA0_IRQHandler                     
00001ad3  TIMA1_IRQHandler                     
00001ad3  TIMG0_IRQHandler                     
00001ad3  TIMG12_IRQHandler                    
00001ad3  TIMG6_IRQHandler                     
00001ad3  TIMG7_IRQHandler                     
00001ad3  TIMG8_IRQHandler                     
00001ad3  UART0_IRQHandler                     
00001ad3  UART1_IRQHandler                     
00001ad3  UART2_IRQHandler                     
00001ad3  UART3_IRQHandler                     
00001ad6  C$$EXIT                              
00001ad7  HOSTexit                             
00001adb  Reset_Handler                        
00001adf  _system_pre_init                     
00001be4  __TI_Handler_Table_Base              
00001bf0  __TI_Handler_Table_Limit             
00001bf8  __TI_CINIT_Base                      
00001c08  __TI_CINIT_Limit                     
00001c08  __TI_CINIT_Warm                      
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200000  rx_buff                              
20200100  Anolog                               
20200110  black                                
20200120  white                                
20200130  Normal                               
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[148 symbols]
