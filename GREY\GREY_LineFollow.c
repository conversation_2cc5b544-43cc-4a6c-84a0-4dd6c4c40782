/*
 * Copyright (c) 2021, Texas Instruments Incorporated
 * All rights reserved.
 * 
 * 循迹小车主程序 - 基于GREY灰度传感器
 * 米醋电子工作室 (Michu Electronics Studio)
 */

#include "ti_msp_dl_config.h"
#include "No_Mcu_Ganv_Grayscale_Sensor_Config.h"
#include "Time.h"
#include "ADC.h"
#include "Uart.h"
#include "Motor.h"
#include "LineFollow.h"

// 全局变量
unsigned short Anolog[8] = {0};
unsigned short Normal[8];
unsigned char rx_buff[256] = {0};

// 校准数据 (根据实际环境调整)
unsigned short white[8] = {430, 696, 583, 416, 691, 746, 369, 224};
unsigned short black[8] = {112, 119, 117, 122, 121, 123, 121, 124};

// 系统状态
typedef enum {
    SYSTEM_CALIBRATION = 0,  // 校准模式
    SYSTEM_LINE_FOLLOW,      // 循迹模式
    SYSTEM_DEBUG,            // 调试模式
    SYSTEM_STOP              // 停止模式
} System_Mode_t;

static System_Mode_t system_mode = SYSTEM_CALIBRATION;
static uint32_t mode_timer = 0;

/********************************************循迹小车系统*******************************************/
/*****************芯片型号: MSPM0G3507 主频: 80MHz ************************************************/
/*****************传感器: 8路灰度传感器 (PB0-PB2地址选择, PA27 ADC输入)*************************/
/*****************电机控制: PWM差速驱动 *********************************************************/
/*****************串口调试: PA10(TX), PA11(RX), 115200bps ************************************/
/********************************************循迹小车系统*******************************************/

/**
 * 函数功能：校准模式处理
 * 说明：采集传感器数据，用于黑白校准
 */
void System_Calibration_Mode(No_MCU_Sensor* sensor, LineFollow_Controller_t* controller)
{
    static uint8_t calibration_step = 0;
    
    // 执行传感器任务
    No_Mcu_Ganv_Sensor_Task_Without_tick(sensor);
    Get_Anolog_Value(sensor, Anolog);
    
    // 打印当前ADC值用于校准
    sprintf((char *)rx_buff, "Calibration Step %d - Analog: %d-%d-%d-%d-%d-%d-%d-%d\r\n",
            calibration_step,
            Anolog[0], Anolog[1], Anolog[2], Anolog[3],
            Anolog[4], Anolog[5], Anolog[6], Anolog[7]);
    uart0_send_string((char *)rx_buff);
    
    calibration_step++;
    
    // 校准完成后进入循迹模式 (10秒后)
    if(calibration_step >= 100) {  // 10秒 * 10Hz
        uart0_send_string("Calibration Complete! Starting Line Following...\r\n");
        system_mode = SYSTEM_LINE_FOLLOW;
        mode_timer = 0;
        
        // 用校准数据重新初始化传感器
        No_MCU_Ganv_Sensor_Init(sensor, white, black);
    }
}

/**
 * 函数功能：循迹模式处理
 */
void System_LineFollow_Mode(No_MCU_Sensor* sensor, LineFollow_Controller_t* controller)
{
    // 执行传感器任务
    No_Mcu_Ganv_Sensor_Task_Without_tick(sensor);
    
    // 循迹控制
    LineFollow_Control(controller, sensor);
    
    // 每100ms打印一次调试信息
    if((mode_timer % 100) == 0) {
        LineFollow_Debug_Print(controller);
    }
}

/**
 * 函数功能：调试模式处理
 */
void System_Debug_Mode(No_MCU_Sensor* sensor, LineFollow_Controller_t* controller)
{
    unsigned char Digital;
    
    // 执行传感器任务
    No_Mcu_Ganv_Sensor_Task_Without_tick(sensor);
    
    // 获取并打印传感器数据
    Digital = Get_Digtal_For_User(sensor);
    sprintf((char *)rx_buff, "Digital: %d-%d-%d-%d-%d-%d-%d-%d\r\n",
            (Digital>>0)&0x01, (Digital>>1)&0x01, (Digital>>2)&0x01, (Digital>>3)&0x01,
            (Digital>>4)&0x01, (Digital>>5)&0x01, (Digital>>6)&0x01, (Digital>>7)&0x01);
    uart0_send_string((char *)rx_buff);
    
    // 获取模拟量
    if(Get_Anolog_Value(sensor, Anolog)) {
        sprintf((char *)rx_buff, "Analog: %d-%d-%d-%d-%d-%d-%d-%d\r\n",
                Anolog[0], Anolog[1], Anolog[2], Anolog[3],
                Anolog[4], Anolog[5], Anolog[6], Anolog[7]);
        uart0_send_string((char *)rx_buff);
    }
    
    // 获取归一化值
    if(Get_Normalize_For_User(sensor, Normal)) {
        sprintf((char *)rx_buff, "Normalize: %d-%d-%d-%d-%d-%d-%d-%d\r\n",
                Normal[0], Normal[1], Normal[2], Normal[3],
                Normal[4], Normal[5], Normal[6], Normal[7]);
        uart0_send_string((char *)rx_buff);
    }
    
    // 停止电机
    Motor_Stop();
}

/**
 * 函数功能：系统模式切换处理
 */
void System_Mode_Switch(void)
{
    // 这里可以添加按键检测或串口命令处理
    // 简单示例：10秒校准后自动进入循迹模式
}

int main(void)
{
    // 系统初始化
    SYSCFG_DL_init();
    
    // 初始化各模块
    No_MCU_Sensor sensor;
    LineFollow_Controller_t line_controller;
    
    uart0_send_string("\r\n=== 循迹小车系统启动 ===\r\n");
    uart0_send_string("芯片: MSPM0G3507 @ 80MHz\r\n");
    uart0_send_string("传感器: 8路灰度传感器\r\n");
    uart0_send_string("控制: PWM差速驱动\r\n");
    uart0_send_string("米醋电子工作室制作\r\n\r\n");
    
    // 初始化传感器 (首次初始化，不带校准值)
    No_MCU_Ganv_Sensor_Init_Frist(&sensor);
    
    // 初始化电机控制
    Motor_Init();
    
    // 初始化循迹控制器
    LineFollow_Init(&line_controller);
    
    uart0_send_string("开始校准模式...\r\n");
    uart0_send_string("请将传感器放在白色和黑色区域进行校准\r\n");
    
    delay_ms(100);
    
    // 主循环
    while (1) {
        // 系统模式处理
        switch(system_mode) {
            case SYSTEM_CALIBRATION:
                System_Calibration_Mode(&sensor, &line_controller);
                break;
                
            case SYSTEM_LINE_FOLLOW:
                System_LineFollow_Mode(&sensor, &line_controller);
                break;
                
            case SYSTEM_DEBUG:
                System_Debug_Mode(&sensor, &line_controller);
                break;
                
            case SYSTEM_STOP:
                Motor_Stop();
                break;
        }
        
        // 模式切换处理
        System_Mode_Switch();
        
        // 清除接收缓冲区
        memset(rx_buff, 0, 256);
        
        // 控制循环频率
        // 经典版传感器: 1ms延时 (1kHz)
        // 青春版传感器: 10ms延时 (100Hz)
        delay_ms(1);
        mode_timer++;
    }
}

/**
 * 中断服务函数示例 (如果需要)
 */
/*
void TIMER_1_INST_IRQHandler(void)
{
    // 定时器中断处理
    // 可用于精确的循迹控制时基
}

void UART_0_INST_IRQHandler(void)
{
    // 串口中断处理
    // 可用于接收控制命令
}
*/