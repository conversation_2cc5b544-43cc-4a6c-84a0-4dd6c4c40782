# 📌 MSPM0G3507 循迹小车引脚配置指南

## 🚨 引脚约束与限制

### ❌ 严禁使用的引脚
根据硬件设计要求，以下引脚**绝对不能**用于用户功能：

```
晶振专用引脚 (硬件强制):
├── PA3  - LFXIN  (32.768kHz晶振输入)
├── PA4  - LFXOUT (32.768kHz晶振输出)
├── PA5  - HFXIN  (高频晶振输入)
└── PA6  - HFXOUT (高频晶振输出)

系统功能引脚:
├── PA18 - BSL (Bootstrap Loader)
├── PA0  - 保留 (用户指定)
└── PA1  - 保留 (用户指定)
```

**⚠️ 重要警告**: 使用上述引脚可能导致系统无法正常启动或时钟异常！

## ✅ 循迹小车完整引脚分配

### 🔍 传感器系统
```
功能模块: 8路灰度传感器
├── ADC输入: PA27 (ADC0_CH0)
├── 地址位0: PB0 (GPIO输出)
├── 地址位1: PB1 (GPIO输出)
└── 地址位2: PB2 (GPIO输出)

工作原理: 3位地址线选择8路传感器 (000-111)
采样方式: 地址复用 + 单ADC通道
```

### ⚡ 电机控制系统
```
左电机控制:
├── PWM输出: PA12 (TIMG0_CC0) - 1kHz, 可调占空比
├── 正转控制: PA13 (GPIO输出) - 高电平有效
└── 反转控制: PA14 (GPIO输出) - 高电平有效

右电机控制:
├── PWM输出: PA15 (TIMG0_CC1) - 1kHz, 可调占空比
├── 正转控制: PA16 (GPIO输出) - 高电平有效
└── 反转控制: PA17 (GPIO输出) - 高电平有效

控制逻辑: PWM调速 + H桥方向控制
速度范围: 0-1000 (对应0%-100%占空比)
```

### 📡 通信与调试
```
串口通信:
├── 发送: PA10 (UART0_TX) - 115200bps
└── 接收: PA11 (UART0_RX) - 115200bps

调试接口:
├── 数据: PA19 (SWDIO) - SWD调试数据
└── 时钟: PA20 (SWCLK) - SWD调试时钟
```

## 🔧 硬件连接指南

### 🎛️ 电机驱动模块连接
推荐使用L298N或类似H桥驱动芯片：

```
MSPM0G3507    →    L298N驱动板    →    电机
─────────────────────────────────────────────
PA12 (PWM)    →    ENA            →    左电机速度
PA13 (DIR1)   →    IN1            →    左电机正转
PA14 (DIR2)   →    IN2            →    左电机反转

PA15 (PWM)    →    ENB            →    右电机速度  
PA16 (DIR1)   →    IN3            →    右电机正转
PA17 (DIR2)   →    IN4            →    右电机反转

VCC           →    12V电源        →    电机供电
GND           →    GND共地        →    系统接地
```

### 🔍 传感器模块连接
8路灰度传感器模块连接：

```
MSPM0G3507    →    传感器模块    →    功能
────────────────────────────────────────
PA27          →    OUT           →    模拟信号输出
PB0           →    A0            →    地址选择位0
PB1           →    A1            →    地址选择位1  
PB2           →    A2            →    地址选择位2
3.3V          →    VCC           →    电源供电
GND           →    GND           →    电源地
```

## ⚙️ 软件配置参数

### PWM参数配置
```c
定时器配置:
├── 时钟源: 80MHz系统时钟
├── 预分频: 80 (分频到1MHz)
├── 周期: 1000计数 (1kHz频率)
└── 占空比: 0-1000 (0%-100%)

实际PWM频率 = 1MHz / 1000 = 1kHz
```

### GPIO配置规则
```c
电机方向控制逻辑:
├── 前进: DIR1=HIGH, DIR2=LOW
├── 后退: DIR1=LOW,  DIR2=HIGH
└── 停止: DIR1=LOW,  DIR2=LOW

地址选择逻辑 (取反):
├── 选择传感器0: A2=1, A1=1, A0=1 (111)
├── 选择传感器1: A2=1, A1=1, A0=0 (110)
└── ...以此类推
```

## 🔍 引脚验证清单

在开始硬件连接前，请确认：

- [ ] ✅ 没有使用PA0, PA1
- [ ] ✅ 没有使用PA3-PA6 (晶振专用)
- [ ] ✅ 没有使用PA18 (BSL功能)
- [ ] ✅ PWM引脚PA12, PA15可用于定时器输出
- [ ] ✅ GPIO引脚PA13, PA14, PA16, PA17可配置为输出
- [ ] ✅ ADC引脚PA27可用于模拟输入
- [ ] ✅ 串口引脚PA10, PA11可用于UART通信

## 🚀 系统资源使用情况

```
已用资源统计:
├── GPIO: 9个引脚 (PB0-PB2, PA13-PA17, PA27)
├── ADC: 1个通道 (ADC0_CH0)
├── TIMER: 2个定时器 (TIMG0-PWM, TIMG12-时基)
├── UART: 1个串口 (UART0)
└── 调试: SWD接口

剩余可用资源:
├── GPIO: 大量可用 (PA2, PA7-PA9, PA21-PA26等)
├── ADC: 多个通道可用
├── TIMER: 多个定时器可用
└── 通信: SPI, I2C等可用
```

## 📊 性能指标

- **PWM频率**: 1kHz (满足电机控制需求)
- **ADC采样**: 125μs/通道 × 8通道 = 1ms (1kHz循迹频率)
- **串口波特率**: 115200bps (充足的调试带宽)
- **系统主频**: 80MHz (高性能实时控制)

## 🔧 故障排除

### 常见问题诊断
1. **电机不转**: 检查方向控制引脚电平和PWM输出
2. **传感器读数异常**: 检查地址选择逻辑和ADC参考电压
3. **串口无输出**: 检查波特率设置和引脚连接
4. **系统无法启动**: 检查是否误用了晶振或BSL引脚

---

**开发单位**: 米醋电子工作室 (Michu Electronics Studio)  
**技术支持**: <EMAIL>  
**版本**: v1.1  
**更新日期**: 2025-01-31