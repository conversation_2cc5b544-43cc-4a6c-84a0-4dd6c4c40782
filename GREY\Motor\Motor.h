#ifndef __MOTOR_H
#define __MOTOR_H

#include "ti_msp_dl_config.h"

/***************************** 电机控制配置 *****************************/
#define MOTOR_MAX_SPEED     1000    // PWM最大占空比值
#define MOTOR_MIN_SPEED     0       // PWM最小占空比值
#define MOTOR_BASE_SPEED    600     // 基础速度
#define MOTOR_MAX_DIFF      300     // 最大速度差值

/***************************** 硬件引脚定义 *****************************/
// 左电机控制引脚 (基于MSPM0G3507引脚表)
#define LEFT_MOTOR_PWM_PORT         GPIOA
#define LEFT_MOTOR_PWM_PIN          DL_GPIO_PIN_12    // PA12 - TIMG0_C0
#define LEFT_MOTOR_DIR1_PORT        GPIOA  
#define LEFT_MOTOR_DIR1_PIN         DL_GPIO_PIN_13    // PA13 - 正转
#define LEFT_MOTOR_DIR2_PORT        GPIOA
#define LEFT_MOTOR_DIR2_PIN         DL_GPIO_PIN_14    // PA14 - 反转

// 右电机控制引脚
#define RIGHT_MOTOR_PWM_PORT        GPIOA
#define RIGHT_MOTOR_PWM_PIN         DL_GPIO_PIN_15    // PA15 - TIMG0_C1  
#define RIGHT_MOTOR_DIR1_PORT       GPIOA
#define RIGHT_MOTOR_DIR1_PIN        DL_GPIO_PIN_16    // PA16 - 正转
#define RIGHT_MOTOR_DIR2_PORT       GPIOA
#define RIGHT_MOTOR_DIR2_PIN        DL_GPIO_PIN_17    // PA17 - 反转

// PWM定时器配置
#define MOTOR_TIMER_INST            TIMG0
#define LEFT_MOTOR_CC_INDEX         DL_TIMER_CC_0_INDEX
#define RIGHT_MOTOR_CC_INDEX        DL_TIMER_CC_1_INDEX

/***************************** 电机方向定义 *****************************/
typedef enum {
    MOTOR_FORWARD = 0,   // 前进
    MOTOR_BACKWARD = 1,  // 后退
    MOTOR_STOP = 2       // 停止
} Motor_Direction_t;

/***************************** 电机结构体 *****************************/
typedef struct {
    uint16_t left_speed;    // 左电机速度
    uint16_t right_speed;   // 右电机速度
    Motor_Direction_t left_dir;   // 左电机方向
    Motor_Direction_t right_dir;  // 右电机方向
} Motor_Control_t;

#ifdef __cplusplus
extern "C" {
#endif

/***************************** 函数声明 *****************************/
// 电机初始化
void Motor_Init(void);

// 电机控制函数
void Motor_Set_Speed(uint16_t left_speed, uint16_t right_speed);
void Motor_Set_Direction(Motor_Direction_t left_dir, Motor_Direction_t right_dir);
void Motor_Control(uint16_t left_speed, uint16_t right_speed, 
                   Motor_Direction_t left_dir, Motor_Direction_t right_dir);

// 高级控制函数
void Motor_Move_Forward(uint16_t speed);
void Motor_Move_Backward(uint16_t speed);
void Motor_Turn_Left(uint16_t speed);
void Motor_Turn_Right(uint16_t speed);
void Motor_Stop(void);

// 差速控制函数
void Motor_Differential_Drive(int16_t left_speed, int16_t right_speed);

#ifdef __cplusplus
}
#endif

#endif /* __MOTOR_H */