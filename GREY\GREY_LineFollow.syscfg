/**
 * 循迹小车系统配置文件 - MSPM0G3507
 * 基于引脚表优化的完整配置
 * 
 * 功能模块：
 * - 8路灰度传感器 (ADC + 地址选择)
 * - 双PWM电机控制
 * - UART调试通信
 * - 定时器基准
 */

/**
 * Import the modules used in this configuration.
 */
const ADC12  = scripting.addModule("/ti/driverlib/ADC12", {}, false);
const ADC121 = ADC12.addInstance();
const GPIO   = scripting.addModule("/ti/driverlib/GPIO", {}, false);
const GPIO1  = GPIO.addInstance();
const GPIO2  = GPIO.addInstance();
const SYSCTL = scripting.addModule("/ti/driverlib/SYSCTL");
const TIMER  = scripting.addModule("/ti/driverlib/TIMER", {}, false);
const TIMER1 = TIMER.addInstance();
const TIMER2 = TIMER.addInstance();
const UART   = scripting.addModule("/ti/driverlib/UART", {}, false);
const UART1  = UART.addInstance();

/**
 * Write custom configuration values to the imported modules.
 */

// 系统时钟配置 (80MHz)
const divider6       = system.clockTree["PLL_CLK2X_DIV"];
divider6.divideValue = 4;

const divider7       = system.clockTree["PLL_PDIV"];
divider7.divideValue = 2;

const divider9       = system.clockTree["UDIV"];
divider9.divideValue = 2;

const multiplier2         = system.clockTree["PLL_QDIV"];
multiplier2.multiplyValue = 10;

const mux8       = system.clockTree["HSCLKMUX"];
mux8.inputSelect = "HSCLKMUX_SYSPLL2X";

// ADC配置 (8路灰度传感器)
ADC121.sampClkDiv                        = "DL_ADC12_CLOCK_DIVIDE_8";
ADC121.powerDownMode                     = "DL_ADC12_POWER_DOWN_MODE_MANUAL";
ADC121.sampleTime0                       = "125us";
ADC121.$name                             = "ADC1";
ADC121.adcMem0_name                      = "ADC_Channel0";
ADC121.peripheral.adcPin0.$assign        = "PA27";
ADC121.adcPin0Config.$name               = "ti_driverlib_gpio_GPIOPinGeneric0";

// 灰度传感器地址选择GPIO
GPIO1.$name                          = "Gray_Address";
GPIO1.associatedPins.create(3);
GPIO1.associatedPins[0].$name        = "PIN_0";
GPIO1.associatedPins[0].assignedPort = "PORTB";
GPIO1.associatedPins[0].assignedPin  = "0";
GPIO1.associatedPins[1].$name        = "PIN_1";
GPIO1.associatedPins[1].assignedPort = "PORTB";
GPIO1.associatedPins[1].assignedPin  = "1";
GPIO1.associatedPins[2].$name        = "PIN_2";
GPIO1.associatedPins[2].assignedPort = "PORTB";
GPIO1.associatedPins[2].assignedPin  = "2";

// 电机控制GPIO (方向控制)
GPIO2.$name                          = "Motor_Control";
GPIO2.associatedPins.create(4);
GPIO2.associatedPins[0].$name        = "LEFT_DIR1";
GPIO2.associatedPins[0].assignedPort = "PORTA";
GPIO2.associatedPins[0].assignedPin  = "13";
GPIO2.associatedPins[0].direction    = "OUTPUT";
GPIO2.associatedPins[0].initialValue = "SET";
GPIO2.associatedPins[1].$name        = "LEFT_DIR2";
GPIO2.associatedPins[1].assignedPort = "PORTA";
GPIO2.associatedPins[1].assignedPin  = "14";
GPIO2.associatedPins[1].direction    = "OUTPUT";
GPIO2.associatedPins[1].initialValue = "CLEAR";
GPIO2.associatedPins[2].$name        = "RIGHT_DIR1";
GPIO2.associatedPins[2].assignedPort = "PORTA";
GPIO2.associatedPins[2].assignedPin  = "16";
GPIO2.associatedPins[2].direction    = "OUTPUT";
GPIO2.associatedPins[2].initialValue = "SET";
GPIO2.associatedPins[3].$name        = "RIGHT_DIR2";
GPIO2.associatedPins[3].assignedPort = "PORTA";
GPIO2.associatedPins[3].assignedPin  = "17";
GPIO2.associatedPins[3].direction    = "OUTPUT";
GPIO2.associatedPins[3].initialValue = "CLEAR";

// 系统控制
SYSCTL.forceDefaultClkConfig = true;
SYSCTL.clockTreeEn           = true;

// 定时器1 - 系统时基 (1ms)
TIMER1.timerPeriod        = "1 ms";
TIMER1.$name              = "TIMER_1";
TIMER1.interrupts         = ["ZERO"];
TIMER1.peripheral.$assign = "TIMG12";

// 定时器2 - PWM电机控制 (1kHz, 80000个计数)
TIMER2.$name                    = "MOTOR_PWM";
TIMER2.timerClkPrescale         = 80;           // 80分频：80MHz/80 = 1MHz
TIMER2.timerPeriod              = "1 ms";       // 1ms周期 = 1kHz频率
TIMER2.timerMode                = "PERIODIC";
TIMER2.pwmMode                  = true;
TIMER2.ccIndex                  = ["0","1"];    // 使用通道0和1
TIMER2.peripheral.$assign       = "TIMG0";

// PWM通道0配置 (左电机 - PA12)
TIMER2.captureCompareValue0     = 0;            // 初始占空比0%
TIMER2.ccIndexFunction0         = "CCValue";
TIMER2.peripheral.ccp0Pin.$assign = "PA12";

// PWM通道1配置 (右电机 - PA15)  
TIMER2.captureCompareValue1     = 0;            // 初始占空比0%
TIMER2.ccIndexFunction1         = "CCValue";
TIMER2.peripheral.ccp1Pin.$assign = "PA15";

// UART配置 (调试通信)
UART1.$name                    = "UART_0";
UART1.targetBaudRate           = 115200;
UART1.peripheral.rxPin.$assign = "PA11";
UART1.peripheral.txPin.$assign = "PA10";

// 调试接口配置
const Board = scripting.addModule("/ti/driverlib/Board", {}, false);

/**
 * Pinmux solution for unlocked pins/peripherals.
 */
ADC121.peripheral.$suggestSolution           = "ADC0";
Board.peripheral.$suggestSolution            = "DEBUGSS";
Board.peripheral.swclkPin.$suggestSolution   = "PA20";
Board.peripheral.swdioPin.$suggestSolution   = "PA19";
GPIO1.associatedPins[0].pin.$suggestSolution = "PB0";
GPIO1.associatedPins[1].pin.$suggestSolution = "PB1";
GPIO1.associatedPins[2].pin.$suggestSolution = "PB2";
GPIO2.associatedPins[0].pin.$suggestSolution = "PA13";
GPIO2.associatedPins[1].pin.$suggestSolution = "PA14";
GPIO2.associatedPins[2].pin.$suggestSolution = "PA16";
GPIO2.associatedPins[3].pin.$suggestSolution = "PA17";
TIMER2.peripheral.ccp0Pin.$suggestSolution   = "PA12";
TIMER2.peripheral.ccp1Pin.$suggestSolution   = "PA15";
UART1.peripheral.$suggestSolution            = "UART0";