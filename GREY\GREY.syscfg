/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "MSPM0G350X" --part "Default" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @v2CliArgs --device "MSPM0G3507" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @versions {"tool":"1.23.0+4000"}
 */

/**
 * Import the modules used in this configuration.
 */
const ADC12  = scripting.addModule("/ti/driverlib/ADC12", {}, false);
const ADC121 = ADC12.addInstance();
const GPIO   = scripting.addModule("/ti/driverlib/GPIO", {}, false);
const GPIO1  = GPIO.addInstance();
const SYSCTL = scripting.addModule("/ti/driverlib/SYSCTL");
const TIMER  = scripting.addModule("/ti/driverlib/TIMER", {}, false);
const TIMER1 = TIMER.addInstance();
const UART   = scripting.addModule("/ti/driverlib/UART", {}, false);
const UART1  = UART.addInstance();

/**
 * Write custom configuration values to the imported modules.
 */
const divider6       = system.clockTree["PLL_CLK2X_DIV"];
divider6.divideValue = 4;

const divider7       = system.clockTree["PLL_PDIV"];
divider7.divideValue = 2;

const divider9       = system.clockTree["UDIV"];
divider9.divideValue = 2;

const multiplier2         = system.clockTree["PLL_QDIV"];
multiplier2.multiplyValue = 10;

const mux8       = system.clockTree["HSCLKMUX"];
mux8.inputSelect = "HSCLKMUX_SYSPLL2X";

ADC121.sampClkDiv                        = "DL_ADC12_CLOCK_DIVIDE_8";
ADC121.powerDownMode                     = "DL_ADC12_POWER_DOWN_MODE_MANUAL";
ADC121.sampleTime0                       = "125us";
ADC121.$name                             = "ADC1";
ADC121.adcMem0_name                      = "ADC_Channel0";
ADC121.peripheral.adcPin0.$assign        = "PA27";
ADC121.adcPin0Config.$name               = "ti_driverlib_gpio_GPIOPinGeneric0";
ADC121.adcPin0Config.hideOutputInversion = scripting.forceWrite(false);

const Board = scripting.addModule("/ti/driverlib/Board", {}, false);

GPIO1.$name                          = "Gray_Address";
GPIO1.associatedPins.create(3);
GPIO1.associatedPins[0].$name        = "PIN_0";
GPIO1.associatedPins[0].assignedPort = "PORTB";
GPIO1.associatedPins[0].assignedPin  = "0";
GPIO1.associatedPins[1].$name        = "PIN_1";
GPIO1.associatedPins[1].assignedPort = "PORTB";
GPIO1.associatedPins[1].assignedPin  = "1";
GPIO1.associatedPins[2].$name        = "PIN_2";
GPIO1.associatedPins[2].assignedPort = "PORTB";
GPIO1.associatedPins[2].assignedPin  = "2";

SYSCTL.forceDefaultClkConfig = true;
SYSCTL.clockTreeEn           = true;

TIMER1.timerPeriod        = "1 ms";
TIMER1.$name              = "TIMER_1";
TIMER1.interrupts         = ["ZERO"];
TIMER1.peripheral.$assign = "TIMG12";

UART1.$name                    = "UART_0";
UART1.targetBaudRate           = 115200;
UART1.peripheral.rxPin.$assign = "PA11";
UART1.peripheral.txPin.$assign = "PA10";
UART1.txPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric1";
UART1.rxPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric2";

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
ADC121.peripheral.$suggestSolution           = "ADC0";
Board.peripheral.$suggestSolution            = "DEBUGSS";
Board.peripheral.swclkPin.$suggestSolution   = "PA20";
Board.peripheral.swdioPin.$suggestSolution   = "PA19";
GPIO1.associatedPins[0].pin.$suggestSolution = "PB0";
GPIO1.associatedPins[1].pin.$suggestSolution = "PB1";
GPIO1.associatedPins[2].pin.$suggestSolution = "PB2";
UART1.peripheral.$suggestSolution            = "UART0";
