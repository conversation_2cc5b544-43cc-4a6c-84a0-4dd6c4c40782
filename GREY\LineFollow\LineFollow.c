#include "LineFollow.h"
#include "Uart.h"
#include <stdio.h>
#include <string.h>

/**
 * 函数功能：循迹控制器初始化
 * 参数：controller - 循迹控制器指针
 */
void LineFollow_Init(LineFollow_Controller_t* controller)
{
    // 初始化传感器权重 (中心为0，两边分别为±3.5)
    // 传感器排列：[0][1][2][3][4][5][6][7]
    // 权重：      -3.5 -2.5 -1.5 -0.5 0.5 1.5 2.5 3.5
    controller->sensor_weights[0] = -3.5f;
    controller->sensor_weights[1] = -2.5f;
    controller->sensor_weights[2] = -1.5f;
    controller->sensor_weights[3] = -0.5f;
    controller->sensor_weights[4] = 0.5f;
    controller->sensor_weights[5] = 1.5f;
    controller->sensor_weights[6] = 2.5f;
    controller->sensor_weights[7] = 3.5f;
    
    // 初始化状态变量
    controller->position = 0.0f;
    controller->last_position = 0.0f;
    controller->state = LINE_FOLLOW_STOP;
    controller->left_motor_speed = 0;
    controller->right_motor_speed = 0;
    controller->line_detected = 0;
    controller->sensor_count = 0;
    
    // 初始化PID控制器
    LineFollow_PID_Init(&controller->pid, PID_KP, PID_KI, PID_KD);
}

/**
 * 函数功能：PID控制器初始化
 */
void LineFollow_PID_Init(PID_Controller_t* pid, float kp, float ki, float kd)
{
    pid->kp = kp;
    pid->ki = ki;
    pid->kd = kd;
    pid->error = 0.0f;
    pid->last_error = 0.0f;
    pid->integral = 0.0f;
    pid->derivative = 0.0f;
    pid->output = 0.0f;
}

/**
 * 函数功能：基于权重计算线的位置
 * 参数：controller - 循迹控制器
 *       digital_data - 8位数字传感器数据
 * 返回：计算出的位置 (-3.5 到 +3.5)
 */
float LineFollow_Calculate_Position(LineFollow_Controller_t* controller, unsigned char digital_data)
{
    float weighted_sum = 0.0f;
    float total_weight = 0.0f;
    uint8_t sensor_count = 0;
    
    // 遍历8个传感器
    for(int i = 0; i < 8; i++) {
        if((digital_data >> i) & 0x01) {  // 如果传感器检测到线
            weighted_sum += controller->sensor_weights[i];
            total_weight += 1.0f;
            sensor_count++;
        }
    }
    
    controller->sensor_count = sensor_count;
    
    // 如果检测到线
    if(total_weight > 0) {
        controller->line_detected = 1;
        controller->position = weighted_sum / total_weight;
        controller->last_position = controller->position;
    } else {
        controller->line_detected = 0;
        // 丢线时保持上次位置
        controller->position = controller->last_position;
    }
    
    return controller->position;
}

/**
 * 函数功能：PID计算
 * 参数：pid - PID控制器
 *       setpoint - 设定值 (通常为0，表示线在中心)
 *       measured_value - 测量值 (传感器计算的位置)
 * 返回：PID输出值
 */
float LineFollow_PID_Calculate(PID_Controller_t* pid, float setpoint, float measured_value)
{
    // 计算误差
    pid->error = setpoint - measured_value;
    
    // 积分项 (限制积分饱和)
    pid->integral += pid->error;
    if(pid->integral > 100.0f) pid->integral = 100.0f;
    if(pid->integral < -100.0f) pid->integral = -100.0f;
    
    // 微分项
    pid->derivative = pid->error - pid->last_error;
    
    // PID输出
    pid->output = pid->kp * pid->error + pid->ki * pid->integral + pid->kd * pid->derivative;
    
    // 更新上次误差
    pid->last_error = pid->error;
    
    return pid->output;
}

/**
 * 函数功能：检测循迹状态
 */
LineFollow_State_t LineFollow_Detect_State(unsigned char digital_data)
{
    uint8_t sensor_count = LineFollow_Count_Active_Sensors(digital_data);
    
    // 没有传感器检测到线 - 丢线
    if(sensor_count == 0) {
        return LINE_FOLLOW_LOST;
    }
    
    // 分析传感器模式
    if(digital_data == 0x18 || digital_data == 0x1C || digital_data == 0x0C) {
        // 中间传感器 - 直行
        return LINE_FOLLOW_STRAIGHT;
    } else if(digital_data & 0x07) {
        // 右侧传感器 - 右转
        if(sensor_count >= 3) return LINE_FOLLOW_SHARP_RIGHT;
        else return LINE_FOLLOW_RIGHT_TURN;
    } else if(digital_data & 0xE0) {
        // 左侧传感器 - 左转
        if(sensor_count >= 3) return LINE_FOLLOW_SHARP_LEFT;
        else return LINE_FOLLOW_LEFT_TURN;
    }
    
    return LINE_FOLLOW_STRAIGHT;
}

/**
 * 函数功能：计算激活的传感器数量
 */
uint8_t LineFollow_Count_Active_Sensors(unsigned char digital_data)
{
    uint8_t count = 0;
    for(int i = 0; i < 8; i++) {
        if((digital_data >> i) & 0x01) {
            count++;
        }
    }
    return count;
}

/**
 * 函数功能：状态机处理
 */
void LineFollow_State_Machine(LineFollow_Controller_t* controller)
{
    switch(controller->state) {
        case LINE_FOLLOW_STRAIGHT:
            // 直行状态 - 使用PID控制
            break;
            
        case LINE_FOLLOW_LEFT_TURN:
            // 左转状态 - 减小左轮速度
            break;
            
        case LINE_FOLLOW_RIGHT_TURN:
            // 右转状态 - 减小右轮速度
            break;
            
        case LINE_FOLLOW_SHARP_LEFT:
            // 急左转 - 左轮反转或停止
            break;
            
        case LINE_FOLLOW_SHARP_RIGHT:
            // 急右转 - 右轮反转或停止
            break;
            
        case LINE_FOLLOW_LOST:
            // 丢线状态 - 根据最后位置决定搜索方向
            break;
            
        case LINE_FOLLOW_STOP:
            // 停止状态
            break;
    }
}

/**
 * 函数功能：电机控制
 */
void LineFollow_Motor_Control(LineFollow_Controller_t* controller)
{
    int16_t left_speed, right_speed;
    float pid_output = controller->pid.output;
    
    // 基于PID输出计算电机速度
    left_speed = LINE_FOLLOW_BASE_SPEED - (int16_t)(pid_output);
    right_speed = LINE_FOLLOW_BASE_SPEED + (int16_t)(pid_output);
    
    // 速度限制
    if(left_speed > LINE_FOLLOW_MAX_SPEED) left_speed = LINE_FOLLOW_MAX_SPEED;
    if(left_speed < LINE_FOLLOW_MIN_SPEED) left_speed = LINE_FOLLOW_MIN_SPEED;
    if(right_speed > LINE_FOLLOW_MAX_SPEED) right_speed = LINE_FOLLOW_MAX_SPEED;
    if(right_speed < LINE_FOLLOW_MIN_SPEED) right_speed = LINE_FOLLOW_MIN_SPEED;
    
    controller->left_motor_speed = (uint16_t)left_speed;
    controller->right_motor_speed = (uint16_t)right_speed;
    
    // 特殊状态处理
    switch(controller->state) {
        case LINE_FOLLOW_LOST:
            // 丢线时搜索
            if(controller->last_position > 0) {
                // 向右搜索
                Motor_Turn_Right(400);
            } else {
                // 向左搜索
                Motor_Turn_Left(400);
            }
            return;
            
        case LINE_FOLLOW_SHARP_LEFT:
            // 急左转
            Motor_Control(200, 600, MOTOR_FORWARD, MOTOR_FORWARD);
            return;
            
        case LINE_FOLLOW_SHARP_RIGHT:
            // 急右转
            Motor_Control(600, 200, MOTOR_FORWARD, MOTOR_FORWARD);
            return;
            
        case LINE_FOLLOW_STOP:
            Motor_Stop();
            return;
            
        default:
            // 正常PID控制
            Motor_Differential_Drive(left_speed, right_speed);
            break;
    }
}

/**
 * 函数功能：主循迹控制函数
 */
void LineFollow_Control(LineFollow_Controller_t* controller, No_MCU_Sensor* sensor)
{
    unsigned char digital_data;
    float position;
    float pid_output;
    
    // 获取传感器数字输出
    digital_data = Get_Digtal_For_User(sensor);
    
    // 计算线的位置
    position = LineFollow_Calculate_Position(controller, digital_data);
    
    // 检测当前状态
    controller->state = LineFollow_Detect_State(digital_data);
    
    // PID计算 (设定值为0，即线在中心)
    pid_output = LineFollow_PID_Calculate(&controller->pid, 0.0f, position);
    
    // 状态机处理
    LineFollow_State_Machine(controller);
    
    // 电机控制
    LineFollow_Motor_Control(controller);
}

/**
 * 函数功能：调试信息打印
 */
void LineFollow_Debug_Print(LineFollow_Controller_t* controller)
{
    char debug_buffer[256];
    
    sprintf(debug_buffer, "Pos:%.2f State:%d PID:%.2f L:%d R:%d Sensors:%d\r\n",
            controller->position,
            controller->state,
            controller->pid.output,
            controller->left_motor_speed,
            controller->right_motor_speed,
            controller->sensor_count);
    
    uart0_send_string(debug_buffer);
}